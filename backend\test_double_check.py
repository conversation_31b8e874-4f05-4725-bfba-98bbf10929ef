"""
Test script for Response Double Check functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.response_double_check import ResponseQualityChecker
from langchain_core.messages import AIMessage

def test_response_quality_checker():
    """Test the response quality checker with various scenarios."""
    
    print("🧪 Testing Response Quality Checker...")
    
    # Initialize the checker
    checker = ResponseQualityChecker()
    
    # Test cases
    test_cases = [
        {
            "name": "Clean medical response",
            "response": "Dựa trên triệu chứng bạn mô tả, có thể đây là dấu hiệu của viêm họng. <PERSON><PERSON>, tôi khuyên bạn nên đến gặp bác sĩ để được khám và chẩn đoán chính xác.",
            "user_query": "Tôi bị đau họng và sốt, có phải viêm họng không?",
            "agent_name": "RAG_AGENT"
        },
        {
            "name": "Response with system artifacts",
            "response": "ORIGINAL USER QUERY: What is diabetes?\nCHATBOT RESPONSE: Diabetes is a chronic condition that affects how your body processes blood sugar.\nREVISED RESPONSE: Diabetes is a chronic condition that affects how your body processes blood sugar.",
            "user_query": "What is diabetes?",
            "agent_name": "RAG_AGENT"
        },
        {
            "name": "Response with debug info",
            "response": "System: Processing medical query...\nDiabetes is a chronic condition. ```debug: confidence=0.85``` Please consult a doctor for proper diagnosis.",
            "user_query": "Tell me about diabetes",
            "agent_name": "WEB_SEARCH_AGENT"
        },
        {
            "name": "AIMessage response",
            "response": AIMessage(content="CHATBOT RESPONSE: Hypertension is high blood pressure. You should monitor it regularly."),
            "user_query": "What is hypertension?",
            "agent_name": "CONVERSATION_AGENT"
        },
        {
            "name": "Empty response",
            "response": "",
            "user_query": "Hello",
            "agent_name": "CONVERSATION_AGENT"
        }
    ]
    
    # Run tests
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"Original: {test_case['response']}")
        
        try:
            improved_response, was_modified = checker.check_response_quality(
                response_content=test_case['response'],
                user_query=test_case['user_query'],
                agent_name=test_case['agent_name']
            )
            
            print(f"Improved: {improved_response}")
            print(f"Modified: {'✅ Yes' if was_modified else '❌ No'}")
            
            # Validate final response
            is_valid = checker.validate_final_response(improved_response)
            print(f"Valid: {'✅ Yes' if is_valid else '❌ No'}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n🎉 Testing completed!")

def test_artifact_detection():
    """Test artifact detection functionality."""
    
    print("\n🔍 Testing Artifact Detection...")
    
    checker = ResponseQualityChecker()
    
    test_responses = [
        "Clean medical response without artifacts",
        "ORIGINAL USER QUERY: What is fever?\nFever is elevated body temperature.",
        "System: Processing... The answer is diabetes.",
        "```system\nDebug info here\n```\nNormal response content"
    ]
    
    for i, response in enumerate(test_responses, 1):
        print(f"\nTest {i}: {response[:50]}...")
        has_artifacts = checker._detect_system_artifacts(response)
        print(f"Has artifacts: {'✅ Yes' if has_artifacts else '❌ No'}")

if __name__ == "__main__":
    test_response_quality_checker()
    test_artifact_detection()
