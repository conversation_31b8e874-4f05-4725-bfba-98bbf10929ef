# Response Double Check Module

## Tổng quan

Module Response Double Check cung cấp một lớp kiểm tra chất lượng cuối cùng cho tất cả các phản hồi của chatbot trước khi gửi đến người dùng. Module này sử dụng LLM với prompting tối ưu để đảm bảo:

1. **Format đầu ra chuẩn**: Loại bỏ các system text không mong muốn
2. **Nội dung chính xác**: Kiểm tra tính chính xác về mặt y tế
3. **Ngữ nghĩa rõ ràng**: <PERSON><PERSON><PERSON> bảo phản hồi dễ hiểu và phù hợp
4. **Tone chuyên nghiệp**: Duy trì giọng điệu phù hợp với chatbot y tế

## Tính năng chính

### 1. Artifact Detection
- Phát hiện và loại bỏ các system text như:
  - "ORIGINAL USER QUERY:"
  - "CHATBOT RESPONSE:"
  - "REVISED RESPONSE:"
  - Debug information
  - System prompts

### 2. Content Quality Check
- Kiểm tra tính chính xác của nội dung y tế
- Đảm bảo phản hồi phù hợp với câu hỏi
- Cải thiện cấu trúc và format

### 3. Response Validation
- Xác thực phản hồi cuối cùng
- Đảm bảo không còn artifacts
- Kiểm tra độ dài và chất lượng nội dung

## Cấu hình

Trong `config.py`, bạn có thể điều chỉnh:

```python
class ResponseDoubleCheckConfig:
    def __init__(self):
        self.enable_double_check = True  # Bật/tắt tính năng
        self.max_retry_attempts = 2      # Số lần thử lại tối đa
        self.llm = ChatGoogleGenerativeAI(
            model=MODEL_CHAT,
            temperature=0.1  # Rất deterministic cho việc kiểm tra chất lượng
        )
```

## Cách sử dụng

### 1. Tích hợp vào workflow

Module đã được tích hợp vào `agent_decision.py` như một bước cuối cùng trong workflow:

```
Agent Response → Guardrails → Double Check → User
```

### 2. Sử dụng độc lập

```python
from agents.response_double_check import ResponseQualityChecker

checker = ResponseQualityChecker()

improved_response, was_modified = checker.check_response_quality(
    response_content="Response cần kiểm tra",
    user_query="Câu hỏi gốc của user",
    agent_name="RAG_AGENT"
)
```

## Workflow Integration

Module được tích hợp vào LangGraph workflow như sau:

1. **analyze_input**: Phân tích input
2. **route_to_agent**: Định tuyến đến agent phù hợp
3. **[AGENT]**: Xử lý bởi agent cụ thể
4. **check_validation**: Kiểm tra cần validation không
5. **apply_guardrails**: Áp dụng guardrails
6. **double_check_response**: ✨ **BƯỚC MỚI** - Kiểm tra chất lượng cuối cùng
7. **END**: Trả về kết quả cho user

## Prompting Strategy

Module sử dụng prompting strategy tối ưu:

### Quality Check Prompt
- Kiểm tra và loại bỏ system artifacts
- Đảm bảo format chuẩn
- Kiểm tra tính chính xác nội dung
- Duy trì tone chuyên nghiệp

### Artifact Detection Prompt
- Phát hiện các pattern không mong muốn
- Trả về kết quả binary (CLEAN/CONTAINS_ARTIFACTS)

## Testing

Chạy test để kiểm tra functionality:

```bash
cd backend
python test_double_check.py
```

Test cases bao gồm:
- Clean medical responses
- Responses with system artifacts
- Debug information cleanup
- AIMessage handling
- Empty response handling

## Lợi ích

1. **Cải thiện UX**: Loại bỏ system text gây nhầm lẫn
2. **Đảm bảo chất lượng**: Kiểm tra nội dung trước khi gửi
3. **Tính nhất quán**: Đảm bảo format đồng nhất
4. **Giảm lỗi**: Phát hiện và sửa lỗi format tự động
5. **Flexibility**: Có thể bật/tắt dễ dàng

## Monitoring

Module ghi log khi:
- Response được cải thiện
- Có lỗi trong quá trình kiểm tra
- Phát hiện artifacts

## Performance

- **Latency**: Thêm ~1-2 giây cho mỗi response
- **Cost**: Chi phí LLM call bổ sung
- **Accuracy**: Cải thiện đáng kể chất lượng output

## Tùy chỉnh

Bạn có thể tùy chỉnh:
- Prompt templates trong `response_quality_checker.py`
- Artifact patterns cần loại bỏ
- Validation rules
- Retry logic
