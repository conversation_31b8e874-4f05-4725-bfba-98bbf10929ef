"""
Response Quality Double Check Module

This module provides a final quality check for all chatbot responses using LLM
to ensure proper formatting, content accuracy, and removal of system artifacts.
"""

import logging
import time
from typing import Dict, Any, Optional, Tuple
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from config import Config

# Initialize configuration
config = Config()

class ResponseQualityChecker:
    """
    A comprehensive response quality checker that performs final validation
    and formatting of chatbot responses before delivery to users.
    """
    
    def __init__(self):
        """Initialize the response quality checker."""
        self.llm = config.response_double_check.llm
        self.logger = logging.getLogger(__name__)
        self.enable_double_check = config.response_double_check.enable_double_check
        self.max_retry_attempts = config.response_double_check.max_retry_attempts
        
        # Quality check prompt template
        self.quality_check_prompt = PromptTemplate.from_template(
            """Bạn là một chuyên gia kiểm tra chất lượng phản hồi cho hệ thống chatbot y tế.
            
Nhiệm vụ của bạn là kiểm tra và cải thiện phản hồi cuối cùng trước khi gửi đến người dùng.

NGUYÊN TẮC KIỂM TRA:
1. Loại bỏ hoàn toàn các system text như "ORIGINAL USER QUERY:", "CHATBOT RESPONSE:", "REVISED RESPONSE:" 
2. Đảm bảo format đầu ra chuẩn, dễ đọc và chuyên nghiệp
3. Kiểm tra tính chính xác về nội dung y tế
4. Đảm bảo ngữ nghĩa rõ ràng và phù hợp với câu hỏi
5. Loại bỏ các thông tin meta, debug info, hoặc system prompts
6. Đảm bảo tone phù hợp với chatbot y tế chuyên nghiệp

CÂU HỎI GỐC CỦA NGƯỜI DÙNG:
{user_query}

PHẢN HỒI CẦN KIỂM TRA:
{response_content}

AGENT ĐÃ XỬ LÝ: {agent_name}

YÊU CẦU ĐẦU RA:
- Nếu phản hồi đã hoàn hảo: trả về EXACTLY nguyên văn nội dung (không thêm bớt gì)
- Nếu cần sửa: trả về phiên bản đã được cải thiện
- KHÔNG bao giờ thêm các prefix như "Đây là phản hồi đã được kiểm tra:" hay tương tự
- KHÔNG thêm các meta-comment về việc kiểm tra
- Chỉ trả về nội dung phản hồi cuối cùng sạch sẽ

PHẢN HỒI CUỐI CÙNG:"""
        )
        
        # Create the quality check chain
        self.quality_check_chain = (
            self.quality_check_prompt 
            | self.llm 
            | StrOutputParser()
        )
        
        # Content validation prompt for detecting system artifacts
        self.artifact_detection_prompt = PromptTemplate.from_template(
            """Kiểm tra xem phản hồi sau có chứa các system artifacts không mong muốn không:

PHẢN HỒI:
{response}

Tìm kiếm các pattern sau:
- "ORIGINAL USER QUERY:"
- "CHATBOT RESPONSE:"  
- "REVISED RESPONSE:"
- "System:"
- "Assistant:"
- "Human:"
- Debug information
- Code snippets không liên quan
- Meta-comments về hệ thống

Trả về:
- "CLEAN" nếu phản hồi sạch sẽ
- "CONTAINS_ARTIFACTS: [mô tả ngắn]" nếu có artifacts

KẾT QUẢ:"""
        )
        
        self.artifact_detection_chain = (
            self.artifact_detection_prompt
            | self.llm
            | StrOutputParser()
        )

    def check_response_quality(
        self, 
        response_content: str, 
        user_query: str = "", 
        agent_name: str = "UNKNOWN",
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, bool]:
        """
        Perform comprehensive quality check on the response.
        
        Args:
            response_content: The response content to check
            user_query: Original user query for context
            agent_name: Name of the agent that generated the response
            context: Additional context information
            
        Returns:
            Tuple of (improved_response, was_modified)
        """
        if not self.enable_double_check:
            return response_content, False

        start_time = time.time()

        try:
            # Extract text content if it's an AIMessage
            if isinstance(response_content, AIMessage):
                response_text = response_content.content
            else:
                response_text = str(response_content)
            
            # Skip check for very short responses or empty content
            if not response_text or len(response_text.strip()) < 10:
                return response_content, False
            
            # First, detect if there are system artifacts
            artifact_check = self._detect_system_artifacts(response_text)
            
            # Perform quality check
            improved_response = self._perform_quality_check(
                response_text, user_query, agent_name
            )
            
            # Determine if response was modified
            was_modified = self._responses_differ(response_text, improved_response)
            
            # Log performance metrics
            processing_time = time.time() - start_time

            if was_modified:
                self.logger.info(f"Response improved by quality checker for agent: {agent_name} (took {processing_time:.2f}s)")
            else:
                self.logger.debug(f"Response passed quality check for agent: {agent_name} (took {processing_time:.2f}s)")

            return improved_response, was_modified
            
        except Exception as e:
            self.logger.error(f"Error in response quality check: {e}")
            # Return original response if quality check fails
            return response_content, False

    def _detect_system_artifacts(self, response: str) -> bool:
        """
        Detect if response contains unwanted system artifacts.
        
        Args:
            response: Response text to check
            
        Returns:
            True if artifacts detected, False otherwise
        """
        try:
            result = self.artifact_detection_chain.invoke({"response": response})
            return not result.strip().upper().startswith("CLEAN")
        except Exception as e:
            self.logger.warning(f"Artifact detection failed: {e}")
            return False

    def _perform_quality_check(
        self, 
        response_text: str, 
        user_query: str, 
        agent_name: str
    ) -> str:
        """
        Perform the main quality check and improvement.
        
        Args:
            response_text: Original response text
            user_query: User's original query
            agent_name: Name of the processing agent
            
        Returns:
            Improved response text
        """
        try:
            improved_response = self.quality_check_chain.invoke({
                "response_content": response_text,
                "user_query": user_query,
                "agent_name": agent_name
            })
            
            # Clean up any remaining artifacts
            cleaned_response = self._clean_response_artifacts(improved_response)
            
            return cleaned_response.strip()
            
        except Exception as e:
            self.logger.error(f"Quality check failed: {e}")
            return response_text

    def _clean_response_artifacts(self, response: str) -> str:
        """
        Clean up common system artifacts from response.
        
        Args:
            response: Response text to clean
            
        Returns:
            Cleaned response text
        """
        # List of patterns to remove
        artifacts_to_remove = [
            "ORIGINAL USER QUERY:",
            "CHATBOT RESPONSE:",
            "REVISED RESPONSE:",
            "PHẢN HỒI CUỐI CÙNG:",
            "System:",
            "Assistant:",
            "Human:",
            "```system",
            "```debug"
        ]
        
        cleaned = response
        
        # Remove artifact patterns
        for artifact in artifacts_to_remove:
            if artifact in cleaned:
                # Remove the artifact and everything before it on the same line
                lines = cleaned.split('\n')
                cleaned_lines = []
                
                for line in lines:
                    if artifact in line:
                        # Find the artifact and remove everything before and including it
                        artifact_pos = line.find(artifact)
                        if artifact_pos >= 0:
                            remaining = line[artifact_pos + len(artifact):].strip()
                            if remaining:
                                cleaned_lines.append(remaining)
                    else:
                        cleaned_lines.append(line)
                
                cleaned = '\n'.join(cleaned_lines)
        
        # Remove empty lines at the beginning and end
        cleaned = cleaned.strip()
        
        # Remove multiple consecutive empty lines
        while '\n\n\n' in cleaned:
            cleaned = cleaned.replace('\n\n\n', '\n\n')
            
        return cleaned

    def _responses_differ(self, original: str, improved: str) -> bool:
        """
        Check if the improved response differs significantly from original.
        
        Args:
            original: Original response text
            improved: Improved response text
            
        Returns:
            True if responses differ significantly
        """
        # Normalize both responses for comparison
        orig_normalized = original.strip().replace('\n', ' ').replace('  ', ' ')
        impr_normalized = improved.strip().replace('\n', ' ').replace('  ', ' ')
        
        # Consider responses different if they differ by more than 5%
        if len(orig_normalized) == 0:
            return len(impr_normalized) > 0
            
        similarity_threshold = 0.95
        
        # Simple similarity check based on length and content
        length_ratio = min(len(orig_normalized), len(impr_normalized)) / max(len(orig_normalized), len(impr_normalized))
        
        if length_ratio < similarity_threshold:
            return True
            
        # Check for content differences
        return orig_normalized.lower() != impr_normalized.lower()

    def validate_final_response(self, response: str) -> bool:
        """
        Perform final validation to ensure response meets quality standards.
        
        Args:
            response: Final response to validate
            
        Returns:
            True if response passes validation
        """
        if not response or len(response.strip()) < 5:
            return False
            
        # Check for remaining system artifacts
        system_indicators = [
            "ORIGINAL USER QUERY:",
            "CHATBOT RESPONSE:",
            "REVISED RESPONSE:",
            "System:",
            "```system"
        ]
        
        response_upper = response.upper()
        for indicator in system_indicators:
            if indicator.upper() in response_upper:
                return False
                
        return True
